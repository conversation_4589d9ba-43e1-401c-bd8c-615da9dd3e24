﻿* {
	vertical-align: middle;
}

body {
	font-family: Arial, sans-serif;
	font-size: 14px;
	margin: 0;
	overflow: hidden;
}

hr {
	border: 0px;
	border-top: 1px solid #ccc;
}

button {
	position: relative;
}

.Panel {
	-moz-user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;

	/* No support for these yet */
	-o-user-select: none;
	user-select: none;
}

.CollapsiblePanel .CollapsiblePanelButton {
	float: left;
	margin-right: 6px;
	width: 0px;
	height: 0px;
	border: 6px solid transparent;
}

.CollapsiblePanel.collapsed .CollapsiblePanelButton {
	margin-top: 2px;
	border-left-color: #bbb;
}

.CollapsiblePanel:not(.collapsed) .CollapsiblePanelButton {
	margin-top: 6px;
	border-top-color: #bbb;
}

.CollapsiblePanel.collapsed .CollapsibleContent {
	display: none;
}

.FancySelect {
	background: #fff;
	border: 1px solid #ccc;
	padding: 0;
	cursor: default;
	overflow: auto;
	outline: none;
}

	.FancySelect .option {
		padding: 4px;
		color: #666;
		white-space: nowrap;
	}

	.FancySelect .option.active {
		background-color: #f8f8f8;
	}

input.Number {
	color: #0080f0;
	font-size: 12px;							/** TODO: Use of !imporant is not ideal **/
	background-color: transparent!important;	/* For now this is a quick fix a rendering issue due to inherited background */
	border: 1px solid transparent;
	padding: 2px;
	cursor: col-resize;
}

#viewport {
	position: absolute;
	top: 32px;
	left: 0px;
	right: 300px;
	bottom: 32px;
}

#menubar {
	position: absolute;
	width: 100%;
	height: 32px;
	background: #eee;
	padding: 0px;
	margin: 0px;
}

	#menubar .menu {
		float: left;
		width: 50px;
		cursor: pointer;
	}

	#menubar .Panel {
		color: #888;
	}

		#menubar .menu .options {
			display: none;
			padding: 5px 0px;
			background: #eee;
			width: 140px;
		}

		#menubar .menu:hover .options {
			display: block;
		}

			#menubar .menu .options hr {
				border-color: #ddd;
			}

			#menubar .menu .options .option {
				color: #666;
				background-color: transparent;
				padding: 5px 10px;
				margin: 0px !important;
			}

				#menubar .menu .options .option:hover {
					color: #fff;
					background-color: #08f;
				}


#sidebar {
	position: absolute;
	right: 0px;
	top: 32px;
	bottom: 0px;
	width: 300px;
	background: #eee;
	overflow: auto;
}

	#sidebar input,
	#sidebar textarea,
	#sidebar select {
		/* background: #ccc; */
	}

	#sidebar .Panel {
		margin-bottom: 10px;
	}

	#sidebar .Panel.collapsed {
		margin-bottom: 0px;
	}

	#sidebar > .Panel {
		color: #888;
		padding: 10px;
		border-top: 1px solid #ccc;
	}

	#sidebar #outliner {
		width: 100%;
		height: 140px;
		color: #444;
		font-size: 12px;
	}

#toolbar {
	position: absolute;
	left: 0px;
	right: 300px;
	bottom: 0px;
	height: 32px;
	background: #eee;
	color: #333;
}

	#toolbar .Panel {
		padding: 4px;
		color: #888;
	}

	#toolbar button {
		margin-right: 6px;
	}


