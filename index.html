<!DOCTYPE html>
<html lang="en">
	<head>
		<title>three.js editor</title>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
	</head>
	<body>
		<link href="css/types.css" rel="stylesheet" />
		<link id="theme" href="css/light.css" rel="stylesheet" />

		<script src="node_modules/recastjs/lib/recast.js"></script>

		<script src="threejs/three.min.js"></script>
		<script src="threejs/system.min.js"></script>

		<script src="threejs/controls/EditorControls.js"></script>
		<script src="threejs/controls/TransformControls.js"></script>
		<script src="threejs/loaders/BabylonLoader.js"></script>
		<script src="threejs/loaders/ColladaLoader.js"></script>
		<script src="threejs/loaders/OBJLoader.js"></script>
		<script src="threejs/loaders/PLYLoader.js"></script>
		<script src="threejs/loaders/STLLoader.js"></script>
		<script src="threejs/loaders/UTF8Loader.js"></script>
		<script src="threejs/loaders/VRMLLoader.js"></script>
		<script src="threejs/loaders/VTKLoader.js"></script>
		<script src="threejs/loaders/ctm/lzma.js"></script>
		<script src="threejs/loaders/ctm/ctm.js"></script>
		<script src="threejs/loaders/ctm/CTMLoader.js"></script>
		<script src="threejs/exporters/SceneExporter.js"></script>
		<script src="threejs/exporters/OBJExporter.js"></script>
		<script src="threejs/exporters/STLExporter.js"></script>
		<script src="threejs/renderers/RaytracingRenderer.js"></script>
		<script src="threejs/renderers/SoftwareRenderer.js"></script>
		<script src="threejs/renderers/SVGRenderer.js"></script>

		<!-- WIP -->

		<script src="threejs/BufferGeometryUtils.js"></script>
	    <script src="threejs/ConvexGeometry.js"></script>

		<script src="threejs/exporters/BufferGeometryExporter.js"></script>
		<script src="threejs/exporters/TypedGeometryExporter.js"></script>
		<script src="threejs/exporters/GeometryExporter.js"></script>
		<script src="threejs/exporters/MaterialExporter.js"></script>
		<script src="threejs/exporters/ObjectExporter.js"></script>
		<script src="threejs/renderers/WebGLRenderer3.js"></script>

		<script src="js/libs/signals.min.js"></script>
		<script src="js/libs/ui.js"></script>
		<script src="js/libs/ui.editor.js"></script>
		<script src="js/libs/ui.three.js"></script>
		<script src="js/libs/underscore-min.js"></script>
		<script src="js/libs/backbone-0.9.1.js"></script>

		<script src="js/Storage.js"></script>

		<script src="js/Editor.js"></script>
		<script src="js/Config.js"></script>
		<script src="js/Loader.js"></script>
		<script src="js/Menubar.js"></script>
		<script src="js/Menubar.File.js"></script>
		<script src="js/Menubar.Edit.js"></script>
		<script src="js/Menubar.Add.js"></script>
		<script src="js/Menubar.View.js"></script>
		<script src="js/Menubar.Help.js"></script>
		<script src="js/Sidebar.js"></script>
		<script src="js/Sidebar.Renderer.js"></script>
		<script src="js/Sidebar.Scene.js"></script>
		<script src="js/Sidebar.Object3D.js"></script>
		<script src="js/Sidebar.Geometry.js"></script>
		<script src="js/Sidebar.Animation.js"></script>
		<script src="js/Sidebar.Geometry.BoxGeometry.js"></script>
		<script src="js/Sidebar.Geometry.CircleGeometry.js"></script>
		<script src="js/Sidebar.Geometry.CylinderGeometry.js"></script>
		<script src="js/Sidebar.Geometry.IcosahedronGeometry.js"></script>
		<script src="js/Sidebar.Geometry.PlaneGeometry.js"></script>
		<script src="js/Sidebar.Geometry.SphereGeometry.js"></script>
		<script src="js/Sidebar.Geometry.TorusGeometry.js"></script>
		<script src="js/Sidebar.Geometry.TorusKnotGeometry.js"></script>
		<script src="js/Sidebar.Material.js"></script>
		<script src="js/Toolbar.js"></script>
		<script src="js/Viewport.js"></script>

		<script src="js/recastjs-editor/Sidebar.Navmesh.js"></script>
		<script src="js/recastjs-editor/Sidebar.NavmeshZone.js"></script>

		<script>

			window.URL = window.URL || window.webkitURL;
			window.BlobBuilder = window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder;

			var editor = new Editor();

			var viewport = new Viewport( editor ).setId( 'viewport' );
			document.body.appendChild( viewport.dom );

			var toolbar = new Toolbar( editor ).setId( 'toolbar' )
			document.body.appendChild( toolbar.dom );

			var menubar = new Menubar( editor ).setId( 'menubar' );
			document.body.appendChild( menubar.dom );

			var sidebar = new Sidebar( editor ).setId( 'sidebar' );
			document.body.appendChild( sidebar.dom );

			//

			editor.setTheme( editor.config.getKey( 'theme' ) );

			editor.storage.init( function () {

				editor.storage.get( function ( state ) {

					if ( state !== undefined ) {

						var loader = new THREE.ObjectLoader();
						var scene = loader.parse( state );

						editor.setScene( scene );

					}

					var selected = editor.config.getKey( 'selected' );

					if ( selected !== undefined ) {

						editor.selectByUuid( selected );

					}

				} );

				//

				var timeout;
				var exporter = new THREE.ObjectExporter();

				var saveState = function ( scene ) {

					clearTimeout( timeout );

					timeout = setTimeout( function () {

						editor.storage.set( exporter.parse( editor.scene ) );

					}, 1000 );

				};

				var signals = editor.signals;

				signals.objectAdded.add( saveState );
				signals.objectChanged.add( saveState );
				signals.objectRemoved.add( saveState );
				signals.materialChanged.add( saveState );
				signals.sceneGraphChanged.add( saveState );

			} );

			//

			document.addEventListener( 'dragover', function ( event ) {

				event.preventDefault();
				event.dataTransfer.dropEffect = 'copy';

			}, false );

			document.addEventListener( 'drop', function ( event ) {

				event.preventDefault();
				editor.loader.loadFile( event.dataTransfer.files[ 0 ] );

			}, false );

			document.addEventListener( 'keydown', function ( event ) {

				switch ( event.keyCode ) {

					case 8: // prevent browser back 
						event.preventDefault();
						break;
					case 46: // delete
						var parent = editor.selected.parent;
						editor.removeObject( editor.selected );
						editor.select( parent );
						break;

				}

			}, false );

			var onWindowResize = function ( event ) {

				editor.signals.windowResize.dispatch();

			};

			window.addEventListener( 'resize', onWindowResize, false );

			onWindowResize();

		</script>
	</body>
</html>
